- name: find_information
  prompt: |
    You are a JSON generator. Respond with raw JSON only, without markdown fences.
    Extract the founding year and a short quote (up to 75 characters) including the founding year from the following markdown text. 
    Only extract from sentences that explicitly mention founding with keywords like 'founded' or 'gegründet'. 
    Examples: 'The company was founded in 1985', '<PERSON> founded the company in 2001', 'Das Unternehmen wurde 1995 gegründet'. 
    Exclude any sentences with relative time references (e.g., 'more than 30 years', 'seit 50 Jahren'). 
    Ignore boilerplate footers like '2025 all rights reserved'. 
    Return a JSON object with keys 'quote' and 'year' (do not include code block markers or ```). If no suitable sentence is found, return both keys as empty strings.

    Markdown: {markdown}