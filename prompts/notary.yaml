- name: find_information
  prompt: |
    Based on the markdown of the company website, perform the following two tasks:

    1. Determine whether the company **itself** provides notary services. Do not infer this based on external partnerships or general mentions of notaries. Look for clear indications that notary services are offered **in-house**, explicitly offering notarial services to clients.
    Search for keywords such as:
    Notar, Notarin, Notariat, Berukundungen, Belaubigungen, Beglaubigungsstelle, Unterschriftsbeglaubigung, Oeffentlicher Notar, Zivilrechtlicher Notar
    (and similar terms in English), but only count them if they clearly refer to **in-house** services.
    
    Exclude mentions that refer **only** to employee titles, roles, or qualifications (e.g., "<PERSON> Mustermann, Notar") unless they are clearly connected to a **described service** the company provides.
    
    2. If notary services are indeed offered by the company itself, extract up to 600 characters of exact quotes from the markdown that support this. 
    If multiple relevant quotes are found, combine them with ellipses (...).
    If no such direct evidence is found, return "NA".
    
    Return your response as raw JSON (do not include code block markers or ```):
    {{
      "provides_notary_services": "yes" or "no",
      "quotes": "..." or "NA"
    }}
    
    Markdown: "{markdown}"

- name: validate_results
  prompt: |
    You are given a text quote (up to 600 characters) that may include services offered by a company, 
    with a focus on notary-related terms (e.g., Notar, Notarin, Notariat, Berukundungen, Belaubigungen, Beglaubigungsstelle, 
    Unterschriftsbeglaubigung, Oeffentlicher Notar, Zivilrechtlicher Notar, and similar terms in English).
    Your task:
    - Shorten the quote to a maximum of 300 characters, preserving only mentions of notary services/products.
    - Preserve any existing ellipses (...) exactly as they appear; do not add new ellipses.
    - Remove any repeated or duplicated phrases or sentences to avoid redundancy.
    - If the quote is irrelevant or contains no service-related content, return NA.
    Quote: "{find_information[quotes]}"
