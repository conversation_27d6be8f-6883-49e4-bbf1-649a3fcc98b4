- name: find_information
  prompt: |
    You are analyzing a firm's website written in markdown format.
    
    Your job is to identify if the firm provides any of the following services, based on specific keywords.

    **Categories & Keywords**:

    1. **Stb (Steueberater = Tax Advisor)**:
       - Steuerberatung, Steuerplanung, Steueroptimierung, Steuererklärung, Steuerliche Beratung, Steuerangelegenheiten

    2. **WP (Wirtschaftsprüfer = Accountant)**:
       - Wirtschaftsprüfung, Accounting, Accounting Services, Finanzbuchhaltung, Buchhaltung, Geschäftsbuchführung, Buchführung, Rechnungswesen, Kontoführung, Debitoren-/Kreditorenbuchhaltung, Jahresabschlüsse, Monatsabschlüsse

    3. **RA (Rechtsanwalt = Lawyer)**:
       - Rechtsanwaltliche Beratung, Rechtliche Beratung, Juristische Beratung, Rechtliche Unterstützung, Rechtliche Betreuung, Rechtsdienstleistungen, Beratung in Rechtsangelegenheiten, Rechtliche Expertise

    4. **UB (Unternehmensberatung = Management Consulting)**:
       - Strategieberatung, Strategieentwicklung, Organisationsberatung, Management Beratung, Management Consulting, Prozess-Optimierung, Unternehmensberatung, Restrukturierungs-Beratung, Restrukturierungen, Nachfolgeberatung, Unternehmenskauf, Unternehmensverkauf

    5. **VW (Vermögensverwaltung = Wealth Management)**:
       - Finanzplanung, Vermögensberatung, Kapitalanlageberatung, Wealth Management, Vermögensstrukturierung

    6. **IT Beratung (IT-Beratung = IT Consulting)**:
       - Digitalisierung, ERP-Beratung, IT-Sicherheit, Datenmanagement, Systemintegration, Digitalization, Digitization, IT Consulting, IT-Beratung, Informationstechnologie

    If other services are described but don't match the above, return a short description of them.

    For each service, respond with:
    - "yes" or "no"
    - Up to 3 quotes (1-2 sentences each) from the markdown that support the "yes" answer. If "no", use '' (empty string). Separate quotes with (...)

    Respond in the following JSON format:
    {{
      "Stb": "yes" or "no",
      "Stb_quote": "..." or "na",
      "WP": "yes" or "no",
      "WP_quote": "..." or "na",
      "RA": "yes" or "no",
      "RA_quote": "..." or "na",
      "UB": "yes" or "no",
      "UB_quote": "..." or "na",
      "VW": "yes" or "no",
      "VW_quote": "..." or "na",
      "IT": "yes" or "no",
      "IT_quote": "..." or "na",
      "Other": "short description of any other services or 'none'"
    }}
    
    Don't output any JSON markers or ```. Output only raw JSON.

    Markdown input:
    {markdown}