- name: find_information 
  prompt: |
    Based on the markdown of the company website, perform the following two tasks:
     
      1. Determine whether the company provides payroll services. Look for keywords such as:
      Lohnbuchhaltung, Lohnbüro, Payroll Services, Gehaltsabrechnung, Lohnabrechnung, Entgeltabrechnung, Personalabrechnung, Personalbuchhaltung
      (and similar terms in English).
     
      2. If payroll services are mentioned, extract up to 600 characters of exact quotes from the markdown that include those keywords.
      If multiple relevant quotes are found, combine them with ellipses (...).
      If no such text is found, return "NA".
     
    Return your response as raw JSON (do not include code block markers or ```):
    {{
      "providesPayrollServices": "yes" or "no",
      "raw_quote": "..." or "NA"
    }}
    But don't preface it with ```json
    Markdown: "{markdown}"

- name: clean_up_shorten_quotes
  prompt: |
   You are given a text quote (up to 600 characters) that may include services offered by a company, 
   with a focus on payroll-related terms (e.g., Lohnbuchhaltung, Lohnbüro, Payroll Services, Gehaltsabrechnung, Lohnab<PERSON>nung,Entgeltabrechnung, Personalabrechnung, Personalbuchhaltung, similar terms in English, etc.).\n\n"
   Your task:
   - Shorten the quote to a maximum of 300 characters, preserving only mentions of payroll services/products (e.g., Lohnbuchhaltung, Lohnbüro, Payroll Services, Gehaltsabrechnung, Lohnabrechnung, Entgeltabrechnung, Personalabrechnung, Personalbuchhaltung, etc.).
   - Preserve any existing ellipses (...) exactly as they appear; do not add new ellipses.
   - Remove any repeated or duplicated phrases or sentences to avoid redundancy.\n"
   - If the quote is irrelevant or contains no service-related content, return NA.\n\n"
    
   Quote: {find_information[raw_quote]}