DATASET_NAME = "csv_location"
VENV_DIRPATH = "/var/jen<PERSON>_home/workspace/venvs/${JOB_NAME}"

def getFileNameWithoutExtension(String filename) {
    return filename.lastIndexOf('.') != -1 ? filename[0..filename.lastIndexOf('.') - 1] : filename
}

def encloseWithQuotes(x) {
    return "\"${x}\""
}

def prepareDataset() {
    try {
        unstash DATASET_NAME
        env.INPUT_FILEPATH = "./input/${csv_location_FILENAME}"
        def directoryPath = env.INPUT_FILEPATH.substring(0, INPUT_FILEPATH.lastIndexOf('/'))
        sh """
        mkdir -p ${directoryPath}
        mv ${DATASET_NAME} ${env.INPUT_FILEPATH}
        """
    } catch (groovy.lang.MissingPropertyException e) {
        error 'You forgot to provide dataset file.'
    }
}

pipeline {
    agent any
    options {
        skipDefaultCheckout(true)
    }
    environment {
        WORKSPACE_JOB_DIR = "${workspace}/${currentBuild.number}"
    }
    parameters {
        stashedFile(name: DATASET_NAME, description: "File to send to Airflow.")
    }
    stages {
        stage('Checkout') {
            steps {
                ws(env.WORKSPACE_JOB_DIR) {
                    checkout scm
                }
            }
        }
        stage('Prepare parameters') {
            steps {
                script {
                    ws(env.WORKSPACE_JOB_DIR) {
                        prepareDataset()
                    }
                }
            }
        }


stage('Run') {
  steps {
    ws(env.WORKSPACE_JOB_DIR) {
      script {
        withCredentials([sshUserPrivateKey(
          credentialsId: 'airflow-ssh-key',   // Jenkins credential ID
          keyFileVariable: 'SSH_KEY'          // path to a temp file with the private key
        )]) {
         sh '''
            # ---- fixed settings ----
            AIRFLOW_HOST="airflow.synergy-impact.de"
            REMOTE_USER="jenkins_ssh"
            INBOX_DIR="/srv/airflow_inbox"
            # ------------------------

            SRC="${INPUT_FILEPATH}"
            ORIG_NAME="$(basename "$SRC")"
            DEST_NAME="${JOB_NAME}_${BUILD_NUMBER}_${ORIG_NAME}"

            echo "Uploading '$SRC' -> ${REMOTE_USER}@${AIRFLOW_HOST}:${INBOX_DIR}/${DEST_NAME}"

            # Make sure the inbox exists (no per-build dirs)
            ssh -i "$SSH_KEY" -o IdentitiesOnly=yes -o StrictHostKeyChecking=accept-new \
              "${REMOTE_USER}@${AIRFLOW_HOST}" "mkdir -p '${INBOX_DIR}'"

            # Simple upload: stream file over SSH (no atomic rename)
            cat "$SRC" | ssh -i "$SSH_KEY" -o IdentitiesOnly=yes -o StrictHostKeyChecking=accept-new \
              "${REMOTE_USER}@${AIRFLOW_HOST}" "cat > '${INBOX_DIR}/${DEST_NAME}'"

            echo "Done. Airflow sees: /opt/airflow/inbox/${DEST_NAME}"
          '''
        }
      }
    }
  }
}






    }
}