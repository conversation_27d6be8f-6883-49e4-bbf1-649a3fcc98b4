pipeline {
  agent {
    node {
    label 'built-in'
      // SAME workspace for every build of this job
      customWorkspace '/var/jenkins_home/workspace/prompts-runner-pipeline'
    }
  }

  options {
           skipDefaultCheckout(true)
   }

  parameters {
    string(
      name: 'urls',
      defaultValue: 'acconsis.de,aktax.de,flick-gmbh.de,fm-steuer.de,forvismazars.com',
      description: 'URLs for which to fetch markdown for RAG. Use "DB" to get from DB.'
    )
    choice(
      name: 'markdown_db_table',
      choices: ['wget2_runs', 'wget_runs', 'scrapegraph_runs', 'playwright_runs'],
      description: 'Which table to take the markdowns from?'
    )
    string(
      name: 'prompt_yaml_filename',
      defaultValue: 'number_of_employees.yaml',
      description: 'YAML file with prompts'
    )
    string(
      name: 'model',
      defaultValue: 'gpt-4o-mini',
      description: 'Which OpenAI model to use'
    )
    string(
      name: 'md_max_length',
      defaultValue: '1_000_000',
      description: 'First N characters of the markdown to use'
    )
    booleanParam(name: 'reinstall_requirements', defaultValue: false, description: 'If checked, (re)create venv and install requirements')
  }

environment {
    PIP_DISABLE_PIP_VERSION_CHECK = '1'
    PYBIN = 'python3.11'
    VENV_DIR = '.venv'    // lives inside the shared workspace
  }

  stages {
    stage('Checkout') {
      steps { checkout scm }
    }

    stage('Prepare venv') {
      steps {
        sh '''
          if [ "${reinstall_requirements}" = "true" ]; then
            echo ">> Reinstall requested — recreating $VENV_DIR"
            rm -rf "$VENV_DIR"
          fi

          if [ ! -d "$VENV_DIR" ]; then
            echo ">> Creating venv at $VENV_DIR"
            $PYBIN -m venv "$VENV_DIR"
            . "$VENV_DIR/bin/activate"
            #python -m pip install --upgrade pip
            pip install -r requirements.txt
          else
            echo ">> Reusing existing venv at $VENV_DIR"
            . "$VENV_DIR/bin/activate"
            python -V
            pip -V
          fi
        '''
      }
    }

    stage('Run driver') {
      steps {
        sh '''
          . "$VENV_DIR/bin/activate"
          python -u -m src.driver \
            --urls "$urls" \
            --prompt_yaml_filename "$prompt_yaml_filename" \
            --markdown_db_table "$markdown_db_table" \
            --model "$model" \
            --md_max_length "$md_max_length"
        '''
      }
    }
  }
}