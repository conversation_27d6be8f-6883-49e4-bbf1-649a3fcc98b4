DATASET_NAME = "csv_location"
VENV_DIRPATH = "/var/jen<PERSON>_home/workspace/venvs/${JOB_NAME}"

def getFileNameWithoutExtension(String filename) {
    return filename.lastIndexOf('.') != -1 ? filename[0..filename.lastIndexOf('.') - 1] : filename
}

def encloseWithQuotes(x) {
    return "\"${x}\""
}

def prepareDataset() {
    try {
        unstash DATASET_NAME
        env.INPUT_FILEPATH = "./input/${csv_location_FILENAME}"
        def directoryPath = env.INPUT_FILEPATH.substring(0, INPUT_FILEPATH.lastIndexOf('/'))
        sh """
        mkdir -p ${directoryPath}
        mv ${DATASET_NAME} ${env.INPUT_FILEPATH}
        """
    } catch (groovy.lang.MissingPropertyException e) {
        error 'You forgot to provide dataset file.'
    }
}

pipeline {
    agent any
    options {
        skipDefaultCheckout(true)
    }
    environment {
        WORKSPACE_JOB_DIR = "${workspace}/${currentBuild.number}"
        UV_PROJECT_ENVIRONMENT = "${VENV_DIRPATH}"
    }
    parameters {
        stashedFile(name: DATASET_NAME, description: "CSV file for manual strip profiles upload. Format: cib_id as first column, followed by data columns.")
        text(name: 'separator', defaultValue: ';', description: "How the columns in the text file are separated.")
        //booleanParam(name: 'refresh_manual_strip_profiles', defaultValue: true, description: "Trigger refresh_manual_strip_profiles after the update.\nDefault 'true'.")
    }
    stages {
        stage('Checkout') {
            steps {
                ws(env.WORKSPACE_JOB_DIR) {
                    checkout scm
                }
            }
        }
        stage('Prepare parameters') {
            steps {
                script {
                    ws(env.WORKSPACE_JOB_DIR) {
                        prepareDataset()
                    }
                }
            }
        }
        stage('Prepare requirements') {
            steps {
                script {
                    ws(env.WORKSPACE_JOB_DIR) {
                        sh """
                        python3 -m venv ${VENV_DIRPATH}/venv
                        . ${VENV_DIRPATH}/venv/bin/activate
                        python3 -m pip install -r requirements.txt
                        """
                    }
                }
            }
        }
        stage('Run') {
            steps {
                ws(env.WORKSPACE_JOB_DIR) {
                    script {
                        // def cmd = """python3 driver.py --prompt_yaml_filename ${encloseWithQuotes(env.INPUT_FILEPATH)} --urls ${encloseWithQuotes(urls)} --markdown_column ${encloseWithQuotes(env.markdown_column)} --model ${encloseWithQuotes(env.model)}"""

                        sh """
                        . ${VENV_DIRPATH}/venv/bin/activate
                        echo File path name: ${env.INPUT_FILEPATH}
                        echo "Processing CSV file for manual strip profiles upload..."
                        export PYTHONPATH=${env.WORKSPACE_JOB_DIR}
                        python3 src/database/process_csv.py ${encloseWithQuotes(env.INPUT_FILEPATH)} ${encloseWithQuotes(env.separator)}
                        """
                    }
                }
            }
        }
    }
}