# prompts-library
This repository contains a collection of prompts to answer various things about companies based mostly on their scraped websites.

## Prompts
Prompts are YAML files under `/prompts` directory. Each file contains a sequence of tasks; downstream tasks can use the output of upstream tasks.

## Running
`driver.py` runs the given prompts file.

## Output
The output is written to a Postgres database.

# DB:

Get content:
```sql
select * from manual_strip_profiles
```

Create a view for the latest (cib_id, column_name) pair:
```sql
CREATE OR REPLACE VIEW latest_manual_strip_profiles AS
SELECT DISTINCT ON (msp.cib_id, msp.column_name)
  msp.id,
  msp.cib_id,
  msp."insertedWhen",
  msp.column_name,
  msp.column_value,
  msp."comment"
FROM manual_strip_profiles msp
ORDER BY
  msp.cib_id,
  msp.column_name,
  msp."insertedWhen" DESC;
```

Delete the view:
```sql
DROP INDEX idx_msp_by_cib_col_insertedwhen_desc;
```

Select from expanded_manual_strip_profiles:
```sql
select * from expanded_manual_strip_profiles 
```
