import subprocess
import sys
import json
import tempfile
import os
from typing import Dict, Any


def execute_python_code(code: str, timeout: int = 30) -> Dict[str, Any]:
    """
    Execute Python code safely in a subprocess and return the results.
    
    Args:
        code: Python code to execute
        timeout: Maximum execution time in seconds
        
    Returns:
        Dict with keys: output, error, success, variables
    """

    print(f"Executing Python code: {code}")

    # Create a temporary file with the code
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        # Wrap the code to capture variables and output
        # Indent the user code properly
        indented_code = '\n'.join('        ' + line for line in code.split('\n'))

        wrapped_code = f"""
import sys
import json
import math
from datetime import datetime, date
import re

# Capture stdout
import io
from contextlib import redirect_stdout

output_buffer = io.StringIO()

try:
    with redirect_stdout(output_buffer):
        # User code starts here
{indented_code}
        # User code ends here
    
    # Capture local variables (excluding built-ins and imports)
    local_vars = {{k: v for k, v in locals().items() 
                  if not k.startswith('_') and k not in ['sys', 'json', 'math', 'datetime', 'date', 're', 'io', 'redirect_stdout', 'output_buffer']}}
    
    # Convert variables to JSON-serializable format
    serializable_vars = {{}}
    for k, v in local_vars.items():
        try:
            json.dumps(v)  # Test if serializable
            serializable_vars[k] = v
        except (TypeError, ValueError):
            serializable_vars[k] = str(v)
    
    result = {{
        'output': output_buffer.getvalue(),
        'error': '',
        'success': True,
        'variables': serializable_vars
    }}
    
except Exception as e:
    result = {{
        'output': output_buffer.getvalue(),
        'error': str(e),
        'success': False,
        'variables': {{}}
    }}

print("PYTHON_EXECUTOR_RESULT:" + json.dumps(result))
"""
        f.write(wrapped_code)
        temp_file_path = f.name
    
    try:
        # Execute the code in subprocess
        process = subprocess.run(
            [sys.executable, temp_file_path],
            capture_output=True,
            text=True,
            timeout=timeout
        )
        
        # Parse the result from stdout
        stdout_lines = process.stdout.strip().split('\n')
        result_line = None
        
        for line in stdout_lines:
            if line.startswith("PYTHON_EXECUTOR_RESULT:"):
                result_line = line[len("PYTHON_EXECUTOR_RESULT:"):]
                break
        
        if result_line:
            result = json.loads(result_line)
        else:
            result = {
                'output': process.stdout,
                'error': process.stderr,
                'success': process.returncode == 0,
                'variables': {}
            }
            
    except subprocess.TimeoutExpired:
        result = {
            'output': '',
            'error': f'Code execution timed out after {timeout} seconds',
            'success': False,
            'variables': {}
        }
    except Exception as e:
        result = {
            'output': '',
            'error': f'Execution error: {str(e)}',
            'success': False,
            'variables': {}
        }
    finally:
        # Clean up temporary file
        try:
            os.unlink(temp_file_path)
        except:
            pass
    
    return result


# Tool definition for LLM
PYTHON_EXECUTOR_TOOL = {
    "type": "function",
    "function": {
        "name": "execute_python_code",
        "description": "Execute Python code for calculations, data processing, and simple operations. Use this when you need to perform mathematical calculations, process lists, count items, or do any computational work that would be easier with code than manual reasoning.",
        "parameters": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "description": "Python code to execute. Can include calculations, list operations, counting, etc. Available modules: math, datetime, re, json. Use print() to output results."
                }
            },
            "required": ["code"]
        }
    }
}
