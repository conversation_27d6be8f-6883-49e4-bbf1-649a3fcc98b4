import src.utils as utils
import src.LangGraphUtils as LangGraphUtils
import uuid
from sqlalchemy import insert
import argparse

parser = argparse.ArgumentParser()

from dotenv import load_dotenv

load_dotenv()

parser.add_argument(
    "--csv_file",
    type=lambda s: [x.strip() for x in s.split(",") if x],
    required=False,
    help="Comma-separated list, e.g. acconsis.de, aktax.de",
)

parser.add_argument(
    "--urls",
    type=lambda s: [x.strip() for x in s.split(",") if x],
    required=False,
    help="Comma-separated list, e.g. acconsis.de, aktax.de",
)

parser.add_argument(
    "--prompt_yaml_filename",
    required=True,          # make it mandatory, drop this line if optional
    help="Path to the YAML prompt file",
)

parser.add_argument(
    "--markdown_db_table",
    required=True,          # make it mandatory, drop this line if optional
    help="Which table to use for the markdown",
)

parser.add_argument(
    "--model",
    required=True,          # make it mandatory, drop this line if optional
    help="Which OpenAI model to use",
)

parser.add_argument(
    "--md_max_length",
    required=True,          # make it mandatory, drop this line if optional
    help="First N characters of the markdown to use",
)

args = parser.parse_args()

#print(args.urls)
#print(args.prompt_yaml_filename)

# to add some test!



#prompts = utils.load_prompt_sequence("prompts/number_of_employees.yaml")
#markdown = utils.load_markdown("markdowns/acconsis.de.md")

prompts = utils.load_prompt_sequence(f"prompts/{args.prompt_yaml_filename}")
#markdown = utils.load_markdown("markdowns/aktax.de.md")

engine = utils.get_engine()
extraction_runs = utils.get_extraction_runs_table()

workflow = utils.create_workflow_state(prompts)

lg = LangGraphUtils.LangGraphUtils(model=args.model)
graph = lg.build_graph(prompts, workflow)




def run_graph_streaming(markdown: str, url: str, prompt_id: str):
    initial_state = {"markdown": markdown}
    state_stream  = graph.stream(initial_state)

    run_id = uuid.uuid4()                        # one id for the whole run

    with engine.begin() as conn:
        try:
            for update in state_stream:
                print(update)

                node_name, payload = next(iter(update.items()))
                step_name = node_name.replace("_node", "")     # "find_information"

                if isinstance(payload, dict) and step_name in payload:
                    answer_dict = payload[step_name]           # {...}
                else:
                    answer_dict = payload                      # already flat

                stmt = (insert(extraction_runs).values(run_id = run_id, step = step_name, answer = answer_dict, url = url, prompt_id=prompt_id,))
                conn.execute(stmt)

                print(f"wrote step={step_name} rows → DB")
        except Exception as e:
            print(e)

print("----")


if args.urls[0] == "DB":
    print("Will take URLs from DB.")
    urls = utils.get_urls_from_db()()
else:
    print("Will take URLs from command-line.")
    urls = args.urls

print(f"Will process {len(urls)} URLs.")
print(f"Will use markdown from {args.markdown_db_table}")
print(f"Will use first {args.md_max_length} characters of the markdown.")

for i, url in enumerate(urls):
    print(f"Processing {i+1}/{len(urls)}: {url}")
    markdown = utils.load_markdown_db(url, table_name=args.markdown_db_table)[0:int(args.md_max_length)]
    if markdown is None:
        print(f"Markdown for {url} not found in DB. Skipping.")
        continue
    print("\t"+ markdown[0:100])
    run_graph_streaming(markdown, url, args.prompt_yaml_filename)
    print("---")


#result = run_graph_streaming(markdown, "test2.com", "payroll_focus.yaml")


