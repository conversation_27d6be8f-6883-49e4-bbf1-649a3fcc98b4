import src.utils as utils
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Text, TIMESTAMP, func, UniqueConstraint
import os

def create_linkedin_txt_table():
    engine = utils.get_engine()
    metadata = MetaData()
    linkedin_txt = Table(
        "linkedin_txt",
        metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('cib_id', String(100), nullable=False),
        Column("linkedin_txt", Text),
        Column("updated_at", TIMESTAMP(timezone=True), server_default=func.now()),
        UniqueConstraint('cib_id', 'updated_at', name='uq_cib_property_updated_linkedin'))

    metadata.create_all(engine)

    return linkedin_txt

def insert_linkedin_txt(cib_id, filepath: str):
    my_table = create_linkedin_txt_table()
    engine = utils.get_engine()

    print(f"Got LinkedIn DB table, will insert LinkedIn text for {cib_id} from {filepath}.")

    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            full_text = f.read()
    except UnicodeDecodeError:
        with open(filepath, 'r', encoding='windows-1252') as f:
            full_text = f.read()

    with engine.begin() as conn:
        stmt = my_table.insert().values(cib_id=cib_id, linkedin_txt=full_text)
        conn.execute(stmt)

    print("Inserted LinkedIn text.")


def process_folder(folder_path):
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith(".txt"):
                full_path = os.path.join(root, file)
                url = os.path.splitext(file)[0]  # remove '.txt'
                insert_linkedin_txt(url, full_path)


process_folder("/Users/<USER>/Downloads/TXTFiles 5")

#insert_linkedin_txt("/Users/<USER>/Downloads/coreva-scientific.com (1).txt")