import os
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Text, TIMESTAMP, func, UniqueConstraint
from sqlalchemy.orm import Session
from dotenv import load_dotenv
from pathlib import Path
import src.utils as utils
import json
from sqlalchemy import text

# Load environment variables
load_dotenv(Path(__file__).resolve().parent.parent / ".env")


def create_manual_strip_profiles_table():
    """Create the manual_strip_profiles table if it doesn't exist"""
    engine = utils.get_engine()
    metadata = MetaData()

    # Define the table schema
    manual_strip_profiles = Table(
        'manual_strip_profiles',
        metadata,
        Column('id', Integer, primary_key=True, autoincrement=True),
        Column('cib_id', String(100), nullable=False),
        Column('property', String(100), nullable=False),
        Column('value', Text, nullable=True),
        Column('updated_at', TIMESTAMP, nullable=False, server_default=func.now()),
        UniqueConstraint('cib_id', 'property', 'updated_at', name='uq_cib_property_updated')
        # source_file omitted as per your note
        # Unique per field update per cib_id per time
        # Note: If you want latest overwrite logic, add unique (cib_id, property) instead
    )

    # Create table if it doesn't exist
    metadata.create_all(engine)

    # Create index manually to include DESC
    with engine.connect() as conn:
        conn.execute(text("""
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM pg_indexes WHERE indexname = 'idx_cib_property_updated_desc'
                ) THEN
                    CREATE INDEX idx_cib_property_updated_desc
                    ON manual_strip_profiles (cib_id, property, updated_at DESC);
                END IF;
            END
            $$;
        """))
        conn.commit()

    return manual_strip_profiles

def get_manual_strip_profiles_table():
    """Get the manual_strip_profiles table object"""
    engine = utils.get_engine()
    metadata = MetaData()

    # Try to reflect existing table, create if doesn't exist
    try:
        metadata.reflect(bind=engine)
        if 'manual_strip_profiles' in metadata.tables:
            return metadata.tables['manual_strip_profiles']
    except Exception:
        pass

    # Create table if reflection failed or table doesn't exist
    return create_manual_strip_profiles_table()


from sqlalchemy import text
import src.utils as utils





