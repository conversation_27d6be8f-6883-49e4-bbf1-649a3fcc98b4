CREATE OR <PERSON><PERSON>LACE FUNCTION refresh_expanded_view()
<PERSON><PERSON><PERSON><PERSON> void AS $$
DECLARE
    col RECORD;
    cols TEXT = '';
    view_sql TEXT;
BEGIN
    FOR col IN SELECT DISTINCT column_name FROM latest_manual_strip_profiles ORDER BY column_name LOOP
        cols := cols || FORMAT(
            'MAX(column_value) FILTER (WHERE column_name = %L) AS %I, ',
            col.column_name,
            col.column_name
        );
    END LOOP;

    cols := left(cols, -2);

    view_sql := FORMAT(
        'CREATE OR REPLACE VIEW expanded_manual_strip_profiles AS
         SELECT cib_id, %s
         FROM manual_strip_profiles
         GROUP BY cib_id;',
        cols
    );

    EXECUTE view_sql;
END $$ LANGUAGE plpgsql;


select refresh_expanded_view();